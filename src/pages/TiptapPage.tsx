import { CommentsPanel } from "@/components/tiptap/comments-panel";
import { Editor } from "@/components/tiptap/editor";
import { Toolbar } from "@/components/tiptap/toolbar";

export const TiptapPage = () => {
  return (
    <div className="min-h-screen bg-editor-bg">
      <div className="flex flex-col px-4 pt-2 gap-y-2 fixed top-0 left-0 right-0 z-10 bg-[#FAFBFD] print:hidden h-[112px]">
        <div className="flex items-center justify-center py-2">
          <h1 className="text-xl font-semibold text-gray-800">
            Simple Text Editor
          </h1>
        </div>
        <Toolbar />
      </div>
      <div className="pt-[114px] print:pt-0">
        <Editor />
      </div>
      <CommentsPanel />
    </div>
  );
};
