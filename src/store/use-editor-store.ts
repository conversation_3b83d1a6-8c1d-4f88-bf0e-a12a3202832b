import { create } from "zustand";
import { type Editor } from "@tiptap/react";

export interface Comment {
  id: string;
  content: string;
  author: string;
  createdAt: Date;
  updatedAt: Date;
}

interface EditorState {
  editor: Editor | null;
  setEditor: (editor: Editor | null) => void;

  // Comment management
  comments: Record<string, Comment>;
  activeCommentId: string | null;
  isCommentsPanelOpen: boolean;

  // Comment actions
  addComment: (id: string, content: string, author?: string) => void;
  updateComment: (id: string, content: string) => void;
  deleteComment: (id: string) => void;
  setActiveCommentId: (id: string | null) => void;
  setCommentsPanelOpen: (open: boolean) => void;
}

export const useEditorStore = create<EditorState>((set, get) => ({
  editor: null,
  setEditor: (editor) => {
    console.log("setting editor");
    return set({ editor });
  },

  // Comment state
  comments: {},
  activeCommentId: null,
  isCommentsPanelOpen: false,

  // Comment actions
  addComment: (id: string, content: string, author = "Anonymous") => {
    const comment: Comment = {
      id,
      content,
      author,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    set((state) => ({
      comments: { ...state.comments, [id]: comment },
    }));
  },

  updateComment: (id: string, content: string) => {
    set((state) => ({
      comments: {
        ...state.comments,
        [id]: {
          ...state.comments[id],
          content,
          updatedAt: new Date(),
        },
      },
    }));
  },

  deleteComment: (id: string) => {
    const { editor } = get();
    if (editor) {
      editor.commands.unsetComment(id);
    }
    set((state) => {
      const newComments = { ...state.comments };
      delete newComments[id];
      return {
        comments: newComments,
        activeCommentId:
          state.activeCommentId === id ? null : state.activeCommentId,
      };
    });
  },

  setActiveCommentId: (id: string | null) => {
    set({ activeCommentId: id });
  },

  setCommentsPanelOpen: (open: boolean) => {
    set({ isCommentsPanelOpen: open });
  },
}));
