import { User } from "@/contexts/auth/types";
import {
  Company,
  ForgotPasswordRequest,
  LoginRequest,
  OfficerUpdateRequest,
  RegisterRequest,
  ResetPasswordRequest,
  VerifyOtpRequest,
} from "./types";
import { TechnologyRequest } from "./types/Technology";
import {
  RegisteredAgent,
  UpdateRegisteredAgentRequest,
} from "./types/RegisteredAgent";
import {
  IntellectualPropertyRequest,
  IntellectualPropertyUpdateRequest,
} from "./types/IntellectualProperty";
import { StockOptionPlanRequest, VestingRequest } from "./types/Stocks";
import { OfficerRequest } from "./types/Officer";
import {
  AuthorizedSharesRequest,
  CompanyAddressRequest,
  CompanyDetails,
  CompanyNameRequest,
} from "./types/Company";
import { Document } from "./types/Document";
import { DocumentFile } from "@/types/capTable";
import {
  DocumentPreparationResponse,
  DocumentSignatureResponse,
  DocumentSignatureSQSResponse,
} from "./types/Document";

const LEGAL_CONCIERGE_BASE_URL = "https://apidev.foundersform.com";

export class APIClient {
  private isRefreshing = false;
  private failedQueue: Array<{
    resolve: (value?: unknown) => void;
    reject: (reason?: unknown) => void;
    config: () => Promise<unknown>;
  }> = [];

  constructor(private baseUrl = LEGAL_CONCIERGE_BASE_URL) {}

  // Helper method to parse error response
  private parseErrorResponse(errorResponse: {
    data?: Array<{ description?: string }>;
  }): string {
    if (errorResponse?.data && Array.isArray(errorResponse.data)) {
      const error = errorResponse.data[0];
      return error.description || "An error occurred. Please try again.";
    }
    return "An unexpected error occurred. Please try again.";
  }

  // Helper method to handle errors consistently
  private handleError(error: unknown): { error: string } {
    console.error("API error:", error);
    if (error instanceof Error) {
      return { error: error.message };
    }
    return { error: "An unexpected error occurred" };
  }

  // Process the queue of failed requests
  private processQueue(error: Error | null = null) {
    this.failedQueue.forEach((promise) => {
      if (error) {
        promise.reject(error);
      } else {
        promise.resolve();
      }
    });

    this.failedQueue = [];
  }

  // Handle 401 Unauthorized errors by refreshing the token
  private async handleUnauthorizedError<T>(
    config: () => Promise<T>
  ): Promise<T> {
    // If already refreshing, add to queue
    if (this.isRefreshing) {
      return new Promise((resolve, reject) => {
        this.failedQueue.push({
          resolve,
          reject,
          config,
        });
      })
        .then(() => {
          return config();
        })
        .catch((err) => {
          throw err;
        });
    }

    this.isRefreshing = true;

    try {
      console.log("Token expired, attempting to refresh...");
      // Try to refresh the token
      const refreshResult = await this.post<
        unknown,
        { data?: unknown; error?: string }
      >("/api/auth/refresh");

      console.log("Token refresh result:", refreshResult);

      if (refreshResult.data == null || refreshResult.error) {
        // If refresh fails, process queue with error
        this.processQueue(new Error("Token refresh failed"));
        throw new Error("Authentication failed. Please log in again.");
      }
      // If refresh succeeds, process queue and retry the original request
      this.processQueue();
      return await config();
    } catch (error) {
      this.processQueue(error);
      throw error;
    } finally {
      this.isRefreshing = false;
    }
  }

  // The post method
  private async post<TRequest, TResponse>(
    path: string,
    body?: TRequest
  ): Promise<TResponse> {
    // Skip token refresh for the refresh endpoint to avoid infinite loops
    const isRefreshEndpoint = path === "/api/auth/refresh";

    const executeRequest = async (): Promise<TResponse> => {
      const res = await fetch(`${this.baseUrl}${path}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: body ? JSON.stringify(body) : undefined,
      });

      if (!res.ok) {
        // If unauthorized and not already trying to refresh, attempt token refresh
        if (res.status === 401 && !isRefreshEndpoint) {
          console.log(
            `401 Unauthorized received for ${path}, attempting token refresh...`
          );
          return this.handleUnauthorizedError(() => this.post(path, body));
        }

        const errorResponse = await res.json();
        const errorMessage = this.parseErrorResponse(errorResponse);
        throw new Error(errorMessage);
      }

      try {
        return await res.json();
      } catch (error) {
        // If the response is not JSON, fallback to an empty object
        return {} as TResponse;
      }
    };

    return executeRequest();
  }

  private async get<TResponse>(path: string): Promise<TResponse> {
    const executeRequest = async (): Promise<TResponse> => {
      console.log(`Making GET request to: ${this.baseUrl}${path}`);
      const res = await fetch(`${this.baseUrl}${path}`, {
        method: "GET",
        headers: {
          Accept: "application/json",
        },
        credentials: "include",
      });

      if (!res.ok) {
        console.error(`Request failed with status: ${res.status}`);

        // If unauthorized, attempt token refresh
        if (res.status === 401) {
          console.log(
            `401 Unauthorized received for ${path}, attempting token refresh...`
          );
          return this.handleUnauthorizedError(() => this.get(path));
        }

        throw new Error(`Request failed: ${res.status}`);
      }

      try {
        const jsonResponse = await res.json();
        console.log(`Raw API response:`, jsonResponse);

        // Check if the response has a data property
        if (jsonResponse.data !== undefined) {
          console.log(`Extracted data from response:`, jsonResponse.data);
          return jsonResponse.data;
        } else {
          // If there's no data property, return the whole response
          console.log(`No data property found, returning whole response`);
          return jsonResponse as TResponse;
        }
      } catch (error) {
        console.error("Error parsing json:", error);
        throw error;
      }
    };

    return executeRequest();
  }

  private async delete<TResponse>(path: string): Promise<TResponse> {
    const executeRequest = async (): Promise<TResponse> => {
      const res = await fetch(`${this.baseUrl}${path}`, {
        method: "DELETE",
        credentials: "include",
      });

      if (!res.ok) {
        // If unauthorized, attempt token refresh
        if (res.status === 401) {
          console.log(
            `401 Unauthorized received for ${path}, attempting token refresh...`
          );
          return this.handleUnauthorizedError(() => this.delete(path));
        }

        const errorResponse = await res.json();
        const errorMessage = this.parseErrorResponse(errorResponse);
        throw new Error(errorMessage);
      }

      try {
        return await res.json();
      } catch (error) {
        console.error("Error parsing json");
        throw new Error("Failed to parse response");
      }
    };

    return executeRequest();
  }

  private async put<TRequest, TResponse>(
    path: string,
    body?: TRequest
  ): Promise<TResponse> {
    const executeRequest = async (): Promise<TResponse> => {
      const res = await fetch(`${this.baseUrl}${path}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: body ? JSON.stringify(body) : undefined,
      });

      if (!res.ok) {
        // If unauthorized, attempt token refresh
        if (res.status === 401) {
          console.log(
            `401 Unauthorized received for ${path}, attempting token refresh...`
          );
          return this.handleUnauthorizedError(() => this.put(path, body));
        }

        const errorResponse = await res.json();
        const errorMessage = this.parseErrorResponse(errorResponse);
        throw new Error(errorMessage);
      }

      try {
        return await res.json();
      } catch (error) {
        console.error("Error parsing json");
        throw new Error("Failed to parse response");
      }
    };

    return executeRequest();
  }

  // Individual API methods with proper error handling
  async register(data: RegisterRequest) {
    try {
      const response = await this.post("/api/auth/register", data);
      return { data: response };
    } catch (error) {
      return { error: error.message }; // Return the error message from the post method
    }
  }

  async login(data: LoginRequest) {
    try {
      const response = await this.post("/api/auth/login", data);
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  async verifyOtp(data: VerifyOtpRequest) {
    try {
      const response = await this.post("/api/auth/verify-otp-get-token", data);
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  async refresh() {
    try {
      const response = await this.post("/api/auth/refresh");
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  async logout() {
    try {
      const response = await this.post("/api/auth/logout");
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  async forgotPassword(data: ForgotPasswordRequest) {
    try {
      const response = await this.post("/api/auth/forgot-password", data);
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  async resetPassword(data: ResetPasswordRequest) {
    try {
      const response = await this.post("/api/auth/reset-password", data);
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  async profile() {
    try {
      const response = await this.get<User>("/api/auth/me");
      console.log("adminresponse", response);
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  async getCompanies() {
    try {
      const response = await this.get<Company[]>("/api/auth/companies");
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }
  async selectCompany(companyId: string) {
    try {
      const response = await this.post("/api/auth/select-company", {
        companyId: companyId,
      });
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  // GET: Get company by ID
  async getCompanyById(id: string) {
    try {
      const response = await this.get<CompanyDetails>(`/api/companies/${id}`);
      return response;
    } catch (error) {
      console.error(`Error getting company with ID ${id}:`, error);
      throw error;
    }
  }

  // PUT: Update company name
  async updateCompanyRegistrationMode(id: string, mode: number) {
    return this.put(`/api/companies/${id}/registermode/${mode}`, null);
  }

  // PUT: Update company name
  async updateCompanyName(id: string, payload: CompanyNameRequest) {
    return this.put(`/api/companies/${id}/name`, payload);
  }

  // PUT: Update company address
  async updateCompanyAddress(id: string, payload: CompanyAddressRequest) {
    return this.put(`/api/companies/${id}/address`, payload);
  }

  // PUT: Update authorized shares
  async updateAuthorizedShares(id: string, payload: AuthorizedSharesRequest) {
    return this.put(`/api/companies/${id}/authorizedshares`, payload);
  }

  // POST: Add officer
  async addOfficer(companyId: string, payload: OfficerRequest) {
    return this.post(`/api/companies/${companyId}/officers`, payload);
  }

  // PUT: Update officer
  async updateOfficer(officerId: string, payload: OfficerUpdateRequest) {
    return this.put(`/api/officers/${officerId}`, payload);
  }

  // DELETE: Delete officer
  async deleteOfficer(officerId: string) {
    return this.delete(`/api/officers/${officerId}`);
  }

  // PUT: Update stock option plan
  async updateStockOptionPlan(id: string, payload: StockOptionPlanRequest) {
    return this.put(`/api/companies/${id}/stockoption`, payload);
  }

  // PUT: Update vesting info
  async updateVesting(id: string, payload: VestingRequest) {
    return this.put(`/api/companies/${id}/vesting`, payload);
  }

  // POST: Add officer IP
  async addOfficerIP(officerId: string, payload: IntellectualPropertyRequest) {
    return this.post(`/api/officers/${officerId}/ips`, payload);
  }

  // PUT: Update officer IP
  async updateOfficerIP(
    ipId: string,
    payload: IntellectualPropertyUpdateRequest
  ) {
    return this.put(`/api/ips/${ipId}`, payload);
  }

  // DELETE: Delete officer IP
  async deleteOfficerIP(ipId: string) {
    return this.delete(`/api/ips/${ipId}`);
  }

  // PUT: Update technology for a company
  async updateTechnology(id: string, payload: TechnologyRequest) {
    return this.put(`/api/companies/${id}/technology`, payload);
  }

  // GET: getRegisteredAgents
  async getRegisteredAgents(companyId: string) {
    try {
      const response = await this.get<RegisteredAgent>(
        `/api/companies/${companyId}/agent`
      );
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  // POST: Update registered agent
  async updateRegisteredAgent(
    id: string,
    payload: UpdateRegisteredAgentRequest
  ) {
    return this.post(`/api/companies/${id}/agent`, payload);
  }

  // Collaborator management
  async getCompanyCollaborators(companyId: string) {
    try {
      const response = await this.get(
        `/api/auth/companies/${companyId}/collaborators`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async deleteCollaborator(collaboratorId: string) {
    try {
      const response = await this.delete(
        `/api/auth/collaborators/${collaboratorId}`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async sendInvite(payload: { email: string; role: string }) {
    try {
      const response = await this.post(`/api/auth/send-invite`, payload);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async inviteCollaborator(
    companyId: string,
    payload: { email: string; role: string }
  ) {
    try {
      const response = await this.post(
        `/api/auth/companies/${companyId}/collaborators`,
        payload
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async updateCollaboratorRole(
    collaboratorId: string,
    payload: { role: string }
  ) {
    try {
      const response = await this.put(
        `/api/auth/collaborators/${collaboratorId}`,
        payload
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Confirm company form completion
  async confirmCompanyForm(id: string) {
    try {
      const response = await this.put(`/api/companies/${id}/comfirm`);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // Document management methods

  // GET: Get all documents for a company
  async getCompanyDocuments(companyId: string) {
    try {
      // Using the endpoint from OpenAPI spec: /api/{id}
      const response = await this.get<{ message: string; data: Document[] }>(
        `/api/companies/${companyId}/documents`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Confirm document review
  async confirmReview(companyId: string) {
    try {
      const response = await this.put<
        Record<string, never>,
        { message: string; data: Document[] }
      >(`/api/companies/${companyId}/comfirmreview`);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Sign company documents
  async signCompanyDocuments(companyId: string) {
    try {
      const response = await this.post<
        Record<string, never>,
        { message: string; data: Document[] }
      >(`/api/companies/${companyId}/documents/sign`);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Prepare documents for signature
  async prepareDocumentForSignature(companyId: string) {
    try {
      const response = await this.post<
        Record<string, never>,
        DocumentPreparationResponse
      >(`/api/companies/${companyId}/documents/preparesign`);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Send document for signature to all signers
  async sendDocumentForSignature(companyId: string) {
    try {
      const response = await this.post<
        Record<string, never>,
        DocumentSignatureResponse
      >(`/api/companies/${companyId}/signature/send`);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Send document for signature using SQS (asynchronous)
  async sendDocumentForSignatureSQS(companyId: string) {
    try {
      const response = await this.post<
        Record<string, never>,
        DocumentSignatureSQSResponse
      >(`/api/companies/${companyId}/signature/sendsqs`);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get signature status for SQS workflow
  async getSignatureStatus(companyId: string) {
    try {
      const response = await this.get<number>(
        `/api/companies/${companyId}/signature/sendstatus`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Generate document for a company
  async generateCompanyDocument(companyId: string) {
    try {
      // Using the endpoint from OpenAPI spec: /api/generate/{id}
      const response = await this.post<
        Record<string, never>,
        { message: string; data: Document[] }
      >(`/api/companies/${companyId}/documents/generate`);
      return { data: response.data };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Get document token for viewing
  async getDocumentToken(payload: { documentId: string; companyId: string }) {
    try {
      console.log("Getting document token from client", payload);
      // Using the endpoint from OpenAPI spec: /api/documents/token
      const response = await this.post<
        { documentId: string },
        { message: string; token: string }
      >(`/api/companies/${payload.companyId}/documents/token`, payload);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Callback for document server
  async documentCallback(payload: Record<string, unknown>) {
    try {
      // Using the endpoint from OpenAPI spec: /api/document-callback
      const response = await this.post<
        Record<string, unknown>,
        { message: string }
      >(`/api/document-callback`, payload);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // Document upload methods
  async getDocumentUploadUrl(
    companyId: string,
    payload: {
      key: string;
      contentType: string;
    }
  ) {
    try {
      const response = await this.post<
        typeof payload,
        { data: { uploadUrl: string; key: string } }
      >(`/api/companies/${companyId}/documents/presigned-url`, payload);
      return { data: response.data };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async saveDocumentMetadata(
    companyId: string,
    payload: {
      name: string;
      key: string;
      size: number;
      type: string;
    }
  ) {
    try {
      const response = await this.post<
        typeof payload,
        {
          name: string;
          key: string;
          size: number;
          type: string;
          uploadedAt: string;
          url: string;
        }
      >(`/api/companies/${companyId}/documents/metadata`, payload);
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // Document methods
  async getIncorporationDocuments(companyId: string) {
    try {
      const response = await this.get<{
        message: string;
        data: Record<string, DocumentFile[]>;
        error: string | null;
        validationErrors: unknown | null;
      }>(`/api/companies/${companyId}/final-documents/incorporation-documents`);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PostIncorporation API methods

  // PUT: Complete EIN task
  async completeEIN(companyId: string) {
    try {
      const response = await this.put(
        `/api/companies/${companyId}/completeein`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Complete business account task
  async completeBusinessAccount(companyId: string) {
    try {
      const response = await this.put(
        `/api/companies/${companyId}/completebusinessaccount`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Complete foreign qualification task
  async completeForeignQualification(companyId: string) {
    try {
      const response = await this.put(
        `/api/companies/${companyId}/completeforeignqualification`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Add foreign qualification state
  async addForeignQualificationState(companyId: string, state: string) {
    try {
      const response = await this.post(
        `/api/companies/${companyId}/foreignqualification/states`,
        state
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // DELETE: Remove foreign qualification state
  async removeForeignQualificationState(id: string) {
    try {
      const response = await this.delete(
        `/api/companies/foreignqualification/states/${id}`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Confirm post-incorporation completion
  async confirmPostIncorporation(companyId: string) {
    try {
      const response = await this.put(
        `/api/companies/${companyId}/postincorporation/confirm`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }
}
