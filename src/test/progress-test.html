<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Progress Bar Test</title>
    <style>
      body {
        font-family: Times New Roman;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      .progress-container {
        margin: 20px 0;
      }
      .progress-bar {
        width: 100%;
        height: 12px;
        background-color: #e5e7eb;
        border-radius: 6px;
        overflow: hidden;
      }
      .progress-fill {
        height: 100%;
        background: linear-gradient(to right, #3b82f6, #1d4ed8);
        border-radius: 6px;
        transition: width 0.5s ease-out;
        min-width: 8px;
      }
      .progress-text {
        display: flex;
        justify-content: space-between;
        margin-top: 8px;
        font-size: 14px;
      }
      .percentage {
        font-weight: bold;
        color: #3b82f6;
        background: #dbeafe;
        padding: 4px 12px;
        border-radius: 20px;
      }
      .controls {
        margin: 20px 0;
      }
      button {
        margin: 5px;
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        background: #3b82f6;
        color: white;
        cursor: pointer;
      }
      button:hover {
        background: #1d4ed8;
      }
      .debug-info {
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 16px;
        margin: 20px 0;
        font-size: 12px;
      }
      .status {
        margin: 10px 0;
        padding: 8px;
        border-radius: 4px;
      }
      .status.processing {
        background: #dbeafe;
        color: #1e40af;
      }
      .status.completed {
        background: #dcfce7;
        color: #166534;
      }
    </style>
  </head>
  <body>
    <h1>SQS Progress Bar Test</h1>
    <p>
      This simulates the real-time progress bar behavior for the document review
      workflow.
    </p>

    <div class="progress-container">
      <div class="status" id="status">Ready to start</div>

      <div class="progress-bar">
        <div class="progress-fill" id="progressFill" style="width: 0%"></div>
      </div>

      <div class="progress-text">
        <span id="progressMessage">Ready to start document review process</span>
        <span class="percentage" id="progressPercentage">0%</span>
      </div>
    </div>

    <div class="controls">
      <button onclick="startProcess()">Start SQS Process</button>
      <button onclick="simulateProgress()">Simulate Progress</button>
      <button onclick="resetProgress()">Reset</button>
    </div>

    <div class="debug-info">
      <h3>Debug Information</h3>
      <div id="debugInfo">
        <p>
          <strong>Current Progress:</strong> <span id="debugProgress">0</span>%
        </p>
        <p><strong>Is Polling:</strong> <span id="debugPolling">No</span></p>
        <p>
          <strong>Last Update:</strong> <span id="debugTimestamp">Never</span>
        </p>
        <p><strong>Status:</strong> <span id="debugStatus">idle</span></p>
      </div>
    </div>

    <script>
      let currentProgress = 0;
      let isPolling = false;
      let pollInterval = null;

      function getProgressMessage(progress) {
        if (progress < 30) {
          return "Preparing documents for signature...";
        } else if (progress < 80) {
          return "Sending documents to signers...";
        } else if (progress < 100) {
          return "Finalizing review process...";
        } else {
          return "Document review process completed!";
        }
      }

      function updateUI() {
        const progressFill = document.getElementById("progressFill");
        const progressMessage = document.getElementById("progressMessage");
        const progressPercentage =
          document.getElementById("progressPercentage");
        const status = document.getElementById("status");

        // Update progress bar
        progressFill.style.width = `${currentProgress}%`;

        // Update message
        progressMessage.textContent = getProgressMessage(currentProgress);

        // Update percentage
        progressPercentage.textContent = `${currentProgress}%`;

        // Update status
        if (currentProgress === 100) {
          status.textContent = "Completed";
          status.className = "status completed";
        } else if (isPolling) {
          status.textContent = "Processing";
          status.className = "status processing";
        } else {
          status.textContent = "Ready to start";
          status.className = "status";
        }

        // Update debug info
        document.getElementById("debugProgress").textContent = currentProgress;
        document.getElementById("debugPolling").textContent = isPolling
          ? "Yes"
          : "No";
        document.getElementById("debugTimestamp").textContent =
          new Date().toLocaleTimeString();
        document.getElementById("debugStatus").textContent =
          currentProgress === 100
            ? "completed"
            : isPolling
              ? "processing"
              : "idle";

        console.log(`[ProgressTest] Progress updated to ${currentProgress}%`);
      }

      function startProcess() {
        console.log("[ProgressTest] Starting SQS process simulation");
        currentProgress = 0;
        isPolling = true;
        updateUI();

        // Simulate immediate polling start
        setTimeout(() => {
          simulateProgress();
        }, 500);
      }

      function simulateProgress() {
        if (!isPolling) {
          isPolling = true;
        }

        // Clear existing interval
        if (pollInterval) {
          clearInterval(pollInterval);
        }

        // Simulate polling every 1.5 seconds
        pollInterval = setInterval(() => {
          if (currentProgress < 100) {
            // Simulate realistic progress increments
            const increment = Math.random() * 15 + 5; // 5-20% increments
            currentProgress = Math.min(100, currentProgress + increment);
            currentProgress = Math.round(currentProgress);

            console.log(
              `[ProgressTest] Simulated API response: ${currentProgress}%`
            );
            updateUI();

            if (currentProgress >= 100) {
              console.log("[ProgressTest] Process completed, stopping polling");
              isPolling = false;
              clearInterval(pollInterval);
              updateUI();
            }
          } else {
            isPolling = false;
            clearInterval(pollInterval);
            updateUI();
          }
        }, 1500);
      }

      function resetProgress() {
        console.log("[ProgressTest] Resetting progress");
        currentProgress = 0;
        isPolling = false;
        if (pollInterval) {
          clearInterval(pollInterval);
          pollInterval = null;
        }
        updateUI();
      }

      // Initialize UI
      updateUI();
    </script>
  </body>
</html>
