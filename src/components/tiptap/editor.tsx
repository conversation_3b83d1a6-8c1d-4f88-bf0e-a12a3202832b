"use client";

import { Editor<PERSON>ontent, useEditor } from "@tiptap/react";
import { StarterKit } from "@tiptap/starter-kit";

import { TaskItem } from "@tiptap/extension-task-item";
import { TaskList } from "@tiptap/extension-task-list";

import {
  PaginationPlus,
  TableCellPlus,
  TableHeaderPlus,
  TablePlus,
  TableRowPlus,
  type MarginConfig,
  type PaperSize,
} from "./extensions/pagination";

import { Image } from "@tiptap/extension-image";

import { FontFamily } from "@tiptap/extension-font-family";
import { TextStyle } from "@tiptap/extension-text-style";
import { Underline } from "@tiptap/extension-underline";

import { Color } from "@tiptap/extension-color";
import { Highlight } from "@tiptap/extension-highlight";

import { TextAlign } from "@tiptap/extension-text-align";

import CommentExtension from "@sereneinserenade/tiptap-comment-extension";
import { Link } from "@tiptap/extension-link";
import { Placeholder } from "@tiptap/extension-placeholder";

import { LEFT_MARGIN_DEFAULT, RIGHT_MARGIN_DEFAULT } from "@/constants/margins";
import { FontSizeExtensions } from "@/extensions/font-size";
import { LineHeightExtension } from "@/extensions/line-height";
import { useEditorStore } from "@/store/use-editor-store";
import { useEffect, useRef, useState } from "react";
import { AlignmentHoverMenu } from "./alignment-hover-menu";
import { CommentDialog } from "./comment-dialog";

interface EditorProps {
  initialContent?: string | undefined;
}

export const Editor = ({ initialContent }: EditorProps) => {
  const [leftMargin] = useState(LEFT_MARGIN_DEFAULT);
  const [rightMargin] = useState(RIGHT_MARGIN_DEFAULT);
  const [isCommentDialogOpen, setIsCommentDialogOpen] = useState(false);
  const [selectedText, setSelectedText] = useState("");

  const { setEditor, setActiveCommentId, setCommentsPanelOpen, addComment } =
    useEditorStore();
  const editorRef = useRef<HTMLDivElement>(null);
  const focusCommentWithActiveId = (commentId: string) => {
    // This will be used to scroll to and highlight the comment in the panel
    const commentElement = document.querySelector(
      `[data-comment-id="${commentId}"]`
    );
    if (commentElement) {
      commentElement.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  };

  const handleCreateComment = () => {
    if (!editor) return;

    const { from, to } = editor.state.selection;
    const text = editor.state.doc.textBetween(from, to);

    if (text.trim()) {
      setSelectedText(text);
      setIsCommentDialogOpen(true);
    }
  };

  const handleCommentSubmit = (
    commentId: string,
    content: string,
    author: string
  ) => {
    if (!editor) return;

    // Add comment to store
    addComment(commentId, content, author);

    // Apply comment to selected text in editor
    editor.chain().focus().setComment(commentId).run();

    // Open comments panel and set active comment
    setCommentsPanelOpen(true);
    setActiveCommentId(commentId);
  };

  const editor = useEditor({
    immediatelyRender: false,
    content: initialContent || "",

    onCreate({ editor }) {
      console.log("on onCreate updated");
      setEditor(editor);
    },
    onDestroy() {
      console.log("on onDestroy updated");
      setEditor(null);
    },
    onUpdate({ editor }) {
      console.log("on onUpdate updated");
      setEditor(editor);
    },
    onSelectionUpdate({ editor }) {
      console.log("on onSelectionUpdate updated");
      setEditor(editor);
    },
    onTransaction({ editor }) {
      console.log("on onTransaction updated");
      setEditor(editor);
    },
    onFocus({ editor }) {
      console.log("on onFocus updated");
      setEditor(editor);
    },
    onBlur({ editor }) {
      console.log("on onBlur updated");
      setEditor(editor);
    },
    onContentError({ editor }) {
      console.log("on onContentError updated");
      setEditor(editor);
    },
    editorProps: {
      attributes: {
        class: "focus:outline-none bg-black",
      },
    },
    extensions: [
      StarterKit,
      TablePlus.configure({
        resizable: true,
      }),
      TableRowPlus,
      TableHeaderPlus,
      TableCellPlus,
      TaskList,
      TaskItem.configure({
        nested: true,
      }),
      Image,
      Underline,
      FontFamily,
      TextStyle,
      Color,
      LineHeightExtension.configure({
        types: ["heading", "paragraph"],
        defaultLineHeight: "1.5",
      }),
      FontSizeExtensions,
      TextAlign.configure({
        types: ["heading", "paragraph"],
      }),
      Link.configure({
        openOnClick: false,
        autolink: true,
        defaultProtocol: "https",
      }),
      Highlight.configure({
        multicolor: true,
      }),
      Placeholder.configure({
        placeholder: "Start typing...",
        emptyEditorClass: "is-editor-empty",
      }),
      PaginationPlus.configure({
        pageHeight: 1123, // A4 height in pixels at 96 DPI (297mm)
        pageGap: 10, // Gap between pages for visual separation
        pageBreakBackground: "#e2e8f0", // Darker gray background for page breaks
        pageHeaderHeight: 0,
        pageGapBorderSize: 1, // Add subtle border
        footerRight: "", // No page numbers
        footerLeft: "", // No page numbers
        headerRight: "", // No headers
        headerLeft: "", // No headers
        defaultMarginConfig: {
          top: 25.4,
          right: 25.4,
          bottom: 25.4,
          left: 25.4,
        }, // Standard A4 margins (1 inch)
        defaultPaperSize: "A4",
      }),
      CommentExtension.configure({
        HTMLAttributes: {
          class: "comment-highlight",
        },
        onCommentActivated: (commentId) => {
          setActiveCommentId(commentId);

          // Only open panel if a comment is actually clicked (commentId exists)
          if (commentId) {
            setCommentsPanelOpen(true);
            setTimeout(() => focusCommentWithActiveId(commentId), 100);
          }
        },
      }),
    ],
  });

  // Expose createComment function globally so it can be called from hover menu
  useEffect(() => {
    if (typeof window !== "undefined") {
      (window as any).createComment = handleCreateComment;
    }
  }, [editor, handleCreateComment]);

  useEffect(() => {
    if (editor && initialContent) {
      editor.commands.setContent(initialContent);
    }
  }, [editor, initialContent]);

  return (
    <div ref={editorRef} className="relative bg-slate-50 min-h-screen">
      <div className="container mx-auto py-8 bg-[#e2e8f0]">
        <EditorContent editor={editor} />
      </div>
      <AlignmentHoverMenu editorRef={editorRef} />

      <CommentDialog
        isOpen={isCommentDialogOpen}
        onClose={() => setIsCommentDialogOpen(false)}
        onSubmit={handleCommentSubmit}
        selectedText={selectedText}
      />
    </div>
  );
};
