"use client";

import { useState } from "react";
import { useEditorStore, type Comment } from "@/store/use-editor-store";
import { 
  MessageSquareIcon, 
  XIcon, 
  EditIcon, 
  TrashIcon, 
  CheckIcon,
  PlusIcon,
  ChevronRightIcon,
  ChevronLeftIcon
} from "lucide-react";
import { cn } from "@/lib/utils";

interface CommentsPanelProps {
  className?: string;
}

export const CommentsPanel = ({ className }: CommentsPanelProps) => {
  const {
    comments,
    activeCommentId,
    isCommentsPanelOpen,
    setActiveCommentId,
    setCommentsPanelOpen,
    updateComment,
    deleteComment,
  } = useEditorStore();

  const [editingCommentId, setEditingCommentId] = useState<string | null>(null);
  const [editContent, setEditContent] = useState("");

  const commentsList = Object.values(comments);

  const handleEditStart = (comment: Comment) => {
    setEditingCommentId(comment.id);
    setEditContent(comment.content);
  };

  const handleEditSave = () => {
    if (editingCommentId && editContent.trim()) {
      updateComment(editingCommentId, editContent.trim());
      setEditingCommentId(null);
      setEditContent("");
    }
  };

  const handleEditCancel = () => {
    setEditingCommentId(null);
    setEditContent("");
  };

  const handleCommentClick = (commentId: string) => {
    setActiveCommentId(commentId);
    // Focus the comment in the editor
    const commentElements = document.querySelectorAll(`[data-comment-id="${commentId}"]`);
    if (commentElements.length > 0) {
      const firstElement = commentElements[0] as HTMLElement;
      firstElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      
      // Temporarily highlight the comment
      firstElement.classList.add('active');
      setTimeout(() => {
        firstElement.classList.remove('active');
      }, 2000);
    }
  };

  const handleDelete = (commentId: string) => {
    if (confirm("Are you sure you want to delete this comment?")) {
      deleteComment(commentId);
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <div className={cn("relative", className)}>
      {/* Toggle Button */}
      <button
        onClick={() => setCommentsPanelOpen(!isCommentsPanelOpen)}
        className={cn(
          "fixed right-4 top-1/2 -translate-y-1/2 z-40 bg-white border border-gray-200 rounded-l-lg shadow-lg p-2 transition-all duration-300",
          isCommentsPanelOpen ? "translate-x-0" : "translate-x-0"
        )}
        title={isCommentsPanelOpen ? "Hide Comments" : "Show Comments"}
      >
        {isCommentsPanelOpen ? (
          <ChevronRightIcon className="w-4 h-4" />
        ) : (
          <ChevronLeftIcon className="w-4 h-4" />
        )}
        {commentsList.length > 0 && (
          <span className="absolute -top-2 -left-2 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {commentsList.length}
          </span>
        )}
      </button>

      {/* Comments Panel */}
      <div
        className={cn(
          "fixed right-0 top-0 h-full w-80 bg-white border-l border-gray-200 shadow-lg z-30 transition-transform duration-300 overflow-hidden",
          isCommentsPanelOpen ? "translate-x-0" : "translate-x-full"
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center gap-2">
            <MessageSquareIcon className="w-5 h-5" />
            <h3 className="font-semibold">Comments</h3>
            <span className="text-sm text-gray-500">({commentsList.length})</span>
          </div>
          <button
            onClick={() => setCommentsPanelOpen(false)}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <XIcon className="w-4 h-4" />
          </button>
        </div>

        {/* Comments List */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {commentsList.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              <MessageSquareIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>No comments yet</p>
              <p className="text-sm">Select text and add a comment to get started</p>
            </div>
          ) : (
            commentsList.map((comment) => (
              <div
                key={comment.id}
                data-comment-id={comment.id}
                className={cn(
                  "border rounded-lg p-3 cursor-pointer transition-all duration-200",
                  activeCommentId === comment.id
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 hover:border-gray-300"
                )}
                onClick={() => handleCommentClick(comment.id)}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <div className="font-medium text-sm">{comment.author}</div>
                    <div className="text-xs text-gray-500">
                      {formatDate(comment.createdAt)}
                      {comment.updatedAt > comment.createdAt && " (edited)"}
                    </div>
                  </div>
                  <div className="flex gap-1">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditStart(comment);
                      }}
                      className="p-1 hover:bg-gray-100 rounded"
                      title="Edit comment"
                    >
                      <EditIcon className="w-3 h-3" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(comment.id);
                      }}
                      className="p-1 hover:bg-red-100 rounded text-red-600"
                      title="Delete comment"
                    >
                      <TrashIcon className="w-3 h-3" />
                    </button>
                  </div>
                </div>

                {editingCommentId === comment.id ? (
                  <div className="space-y-2">
                    <textarea
                      value={editContent}
                      onChange={(e) => setEditContent(e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded text-sm resize-none"
                      rows={3}
                      placeholder="Edit your comment..."
                      autoFocus
                      onClick={(e) => e.stopPropagation()}
                    />
                    <div className="flex gap-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditSave();
                        }}
                        className="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
                      >
                        <CheckIcon className="w-3 h-3" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditCancel();
                        }}
                        className="px-2 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600"
                      >
                        <XIcon className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="text-sm text-gray-700 whitespace-pre-wrap">
                    {comment.content}
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};
