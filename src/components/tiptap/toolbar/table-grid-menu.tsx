import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useEditorStore } from "@/store/use-editor-store";
import { TableIcon } from "lucide-react";
import { useEffect, useRef, useState } from "react";

export const TableGridMenu = () => {
  const { editor } = useEditorStore();

  const [open, setOpen] = useState(false);

  const buttonRef = useRef<HTMLButtonElement | null>(null);
  const popupRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popupRef.current &&
        !popupRef.current.contains(event.target as Node) &&
        !buttonRef.current?.contains(event.target as Node)
      ) {
        setOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <button
          ref={buttonRef}
          className="h-7 min-w-7 shrink-0 flex items-center justify-center rounded-sm hover:bg-neutral-200/80"
        >
          <TableIcon className="size-4" />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="p-1 flex flex-col gap-y-1" ref={popupRef}>
        <TableGridSelector
          maxRows={10}
          maxCols={10}
          onSelect={(rows, cols) => {
            setOpen(false);
            editor?.chain().focus().insertTable({ rows, cols }).run();
          }}
        />
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

function TableGridSelector({
  maxRows = 10,
  maxCols = 10,
  onSelect,
}: {
  maxRows?: number;
  maxCols?: number;
  onSelect: (rows: number, cols: number) => void;
}) {
  const [hoveredRows, setHoveredRows] = useState(0);
  const [hoveredCols, setHoveredCols] = useState(0);

  return (
    <div className="p-4">
      <div className="mb-2 text-sm text-center">
        {hoveredCols > 0 && hoveredRows > 0
          ? `${hoveredCols} × ${hoveredRows}`
          : "Select size"}
      </div>
      <div
        className="grid gap-1"
        style={{ gridTemplateColumns: `repeat(${maxCols}, 20px)` }}
      >
        {Array.from({ length: maxRows * maxCols }).map((_, i) => {
          const row = Math.floor(i / maxCols) + 1;
          const col = (i % maxCols) + 1;
          const isSelected = row <= hoveredRows && col <= hoveredCols;

          return (
            <div
              key={i}
              onMouseEnter={() => {
                setHoveredRows(row);
                setHoveredCols(col);
              }}
              onClick={() => onSelect(row, col)}
              className={`w-5 h-5 border rounded-sm cursor-pointer ${
                isSelected ? "bg-blue-500" : "bg-gray-100 hover:bg-gray-200"
              }`}
            />
          );
        })}
      </div>
    </div>
  );
}
