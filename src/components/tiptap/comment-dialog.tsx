"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useState } from "react";

interface CommentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (commentId: string, content: string, author: string) => void;
  selectedText?: string;
}

export const CommentDialog = ({
  isOpen,
  onClose,
  onSubmit,
  selectedText,
}: CommentDialogProps) => {
  const [content, setContent] = useState("");

  const handleSubmit = () => {
    if (content.trim()) {
      const commentId = `comment-${Date.now()}-${Math.random()
        .toString(36)
        .substr(2, 9)}`;
      onSubmit(commentId, content.trim(), "You");
      setContent("");
      onClose();
    }
  };

  const handleClose = () => {
    setContent("");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add Comment</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {selectedText && (
            <div className="p-3 bg-gray-50 rounded-lg">
              <Label className="text-sm font-medium text-gray-700">
                Selected text:
              </Label>
              <p className="text-sm text-gray-600 mt-1 italic">
                "{selectedText}"
              </p>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="comment">Comment</Label>
            <Textarea
              id="comment"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="Write your comment here..."
              className="w-full min-h-[100px] resize-none"
              autoFocus
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={!content.trim()}>
            Add Comment
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
