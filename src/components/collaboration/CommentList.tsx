import React from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import CommentItem from "./CommentItem";
import EmptyCommentState from "./EmptyCommentState";
import { Comment } from "./types";
import { InfoIcon } from "lucide-react";

interface CommentListProps {
  comments: Comment[];
  onResolve: (id: string) => void;
  isUserLoggedIn: boolean;
  loading: boolean;
  showResolved: boolean;
  isDashboard?: boolean;
}

const CommentList: React.FC<CommentListProps> = ({
  comments,
  onResolve,
  isUserLoggedIn,
  loading,
  showResolved,
  isDashboard = false,
}) => {
  const filteredComments = showResolved
    ? comments
    : comments.filter((comment) => !comment.resolved);

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="text-center">
          <div className="inline-block h-6 w-6 animate-spin rounded-full border-2 border-solid border-current border-r-transparent"></div>
          <p className="mt-2 text-sm text-gray-500">Loading comments...</p>
        </div>
      </div>
    );
  }

  if (filteredComments.length === 0) {
    return <EmptyCommentState showResolved={showResolved} />;
  }

  return (
    <div className="flex-1 flex flex-col">
      {isDashboard && (
        <div className="mb-4 p-3 bg-blue-50 rounded-md text-sm text-blue-700 flex items-start">
          <InfoIcon className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
          <div>
            <p>
              <strong>Note:</strong> Comments can only be added and resolved directly on
              documents in the signing screen.
            </p>
            <p className="mt-1">
              This view is for reference only.
            </p>
          </div>
        </div>
      )}

      <ScrollArea className="flex-1 h-[calc(100vh-20rem)]">
        <div className="space-y-3 pr-2">
          {filteredComments.map((comment) => (
            <CommentItem
              key={comment.id}
              comment={comment}
              onResolve={onResolve}
              isUserLoggedIn={isUserLoggedIn}
              isDashboard={isDashboard}
            />
          ))}
        </div>
      </ScrollArea>
    </div>
  );
};

export default CommentList;
